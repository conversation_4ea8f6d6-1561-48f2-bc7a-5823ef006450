package com.mira.user.config;

import feign.Feign;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.ratelimiter.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;

/**
 * Feign Resilience4j 配置
 * 为 Feign 客户端配置限流和熔断功能
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass({Feign.class, CircuitBreaker.class, RateLimiter.class})
@ConditionalOnProperty(value = "feign.resilience4j.enabled", havingValue = "true", matchIfMissing = true)
public class FeignResilience4jConfig {

    /**
     * 注意：由于我们现在使用注解方式（@CircuitBreaker, @RateLimiter）
     * 而不是 Feign 级别的装饰器，所以这个配置主要用于确保 Resilience4j
     * 与 Feign 的兼容性
     */
    @Bean
    @Primary
    @Scope("prototype")
    public Feign.Builder resilience4jFeignBuilder() {
        log.info("创建标准 Feign Builder（Resilience4j 通过注解方式应用）");

        // 返回标准的 Feign Builder，Resilience4j 功能通过方法注解实现
        return Feign.builder();
    }
}