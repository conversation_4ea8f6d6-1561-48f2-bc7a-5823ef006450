spring:
  application:
    name: mira-job
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    loadbalancer:
      nacos:
        enabled: true
  sleuth:
    traceId128: true
    sampler:
      probability: 0.1

feign:
  client:
    config:
      default:
        # 连接超时时间
        connectTimeout: 10000
        # 读取超时时间
        readTimeout: 30000
  # 不使用 httpclient
  httpclient:
    enabled: false
  # 使用 okhttp
  okhttp:
    enabled: true

server:
  port: 8087
  shutdown: graceful
  tomcat:
    threads:
      # 最大工作线程数
      max: 200
      # 最小空闲线程数
      min-spare: 20
    # 最大连接数
    max-connections: 8192
    # 等待队列长度
    accept-count: 100
    # 连接超时时间(毫秒)
    connection-timeout: 30000
    # Keep-Alive超时
    keep-alive-timeout: 60000
    # 单连接最大Keep-Alive请求数
    max-keep-alive-requests: 200
    # 处理器缓存
    processor-cache: 200

xxl:
  job:
    executor:
      # 有效使用该地址作为注册地址 为空使用内嵌服务地址
      address: ''
      # 执行器IP 默认自动获取
      ip: ''
      # 执行器端口 小于等于0 自动获取 ，默认 9999 ，配置多个执行器时，需要配置不同的执行器端口
      port: 9999
      # 执行器日志保持天数 -1永久生效
      logretentiondays: 30
      # 执行器日志文件保持地址 ，为空使用默认保存地址
      logpath: log/job
    admin:
      # 调度中心部署地址，多个配置 ，分割
      addresses: https://manage.miracare.dev/xxl-job-admin
      # 执行器token
    accessToken: jobappkaier2020YZtoken

log:
  path: /root/logs/mira-job/