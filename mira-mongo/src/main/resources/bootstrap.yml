spring:
  application:
    name: mira-mongo
  profiles:
    active: dev
  mvc:
    pathmatch:
      # spring boot 2.6 以上兼容 swagger3
      matching-strategy: ant_path_matcher
  cloud:
    # 负载均衡
    loadbalancer:
      nacos:
        enabled: true
feign:
  client:
    config:
      default:
        # 连接超时时间
        connectTimeout: 10000
        # 读取超时时间
        readTimeout: 30000
  # 禁用httpclient
  httpclient:
    enabled: false
  # 启用okhttp
  okhttp:
    enabled: true

server:
  port: 8078
  shutdown: graceful
  tomcat:
    threads:
      # 最大工作线程数
      max: 200
      # 最小空闲线程数
      min-spare: 20
    # 最大连接数
    max-connections: 8192
    # 等待队列长度
    accept-count: 100
    # 连接超时时间(毫秒)
    connection-timeout: 30000
    # Keep-Alive超时
    keep-alive-timeout: 60000
    # 单连接最大Keep-Alive请求数
    max-keep-alive-requests: 200
    # 处理器缓存
    processor-cache: 200

logging:
  config: classpath:logback/logback-${spring.profiles.active}.xml